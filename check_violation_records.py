#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的违规记录状态
找出处于未知状态的记录
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'api'))

from app import create_app, db
from app.violations.models import ViolationRecord
from app.bikes.models import Bikes
from app.users.models import Users
from sqlalchemy import func
from datetime import datetime

def check_violation_records():
    """检查违规记录状态"""
    app = create_app()

    with app.app_context():
        print("=" * 80)
        print("违规记录状态检查")
        print("=" * 80)

        # 1. 查询所有违规记录
        all_records = ViolationRecord.query.all()
        print(f"\n📊 数据库中共有 {len(all_records)} 条违规记录")

        # 2. 按状态统计
        status_stats = db.session.query(
            ViolationRecord.status,
            func.count(ViolationRecord.id).label('count')
        ).group_by(ViolationRecord.status).all()

        print(f"\n📈 违规记录状态统计:")
        status_map = {
            0: '待审核',
            1: '已处理',
            2: '申诉中',
            3: '已忽略'
        }

        known_statuses = set()
        for status, count in status_stats:
            status_text = status_map.get(status, f'未知状态({status})')
            print(f"   状态 {status} ({status_text}): {count} 条记录")
            known_statuses.add(status)

        # 3. 找出未知状态的记录
        unknown_statuses = []
        for status, count in status_stats:
            if status not in status_map:
                unknown_statuses.append((status, count))

        if unknown_statuses:
            print(f"\n⚠️  发现 {len(unknown_statuses)} 种未知状态:")
            for status, count in unknown_statuses:
                print(f"   状态 {status}: {count} 条记录")
        else:
            print(f"\n✅ 所有记录状态都在预期范围内")

        # 4. 详细检查每条记录
        print(f"\n🔍 详细记录信息:")
        print("-" * 80)

        for i, record in enumerate(all_records, 1):
            status_text = status_map.get(record.status, f'未知状态({record.status})')

            # 获取相关信息
            vehicle_info = "未知车辆"
            user_info = "未知用户"

            if record.bike_id:
                vehicle = Bikes.query.get(record.bike_id)
                if vehicle:
                    vehicle_info = f"车牌号: {vehicle.b_num}"
                    if vehicle.belong_to:
                        user = Users.query.get(vehicle.belong_to)
                        if user:
                            user_info = f"用户: {user.u_name} (ID: {user.u_id})"

            print(f"记录 {i}:")
            print(f"   ID: {record.id}")
            print(f"   状态: {record.status} ({status_text})")
            print(f"   违规类型: {record.violation_type}")
            print(f"   车辆信息: {vehicle_info}")
            print(f"   {user_info}")
            print(f"   违规时间: {record.violation_time}")
            print(f"   创建时间: {record.created_at}")
            print(f"   描述: {record.description[:50]}..." if record.description and len(record.description) > 50 else f"   描述: {record.description}")

            # 标记未知状态
            if record.status not in status_map:
                print(f"   ⚠️  这是一条未知状态的记录！")

            print("-" * 40)

        # 5. 检查数据完整性
        print(f"\n🔍 数据完整性检查:")

        # 检查是否有无效的车辆ID
        invalid_vehicle_records = []
        for record in all_records:
            if record.bike_id:
                vehicle = Bikes.query.get(record.bike_id)
                if not vehicle:
                    invalid_vehicle_records.append(record)

        if invalid_vehicle_records:
            print(f"   ⚠️  发现 {len(invalid_vehicle_records)} 条记录引用了不存在的车辆:")
            for record in invalid_vehicle_records:
                print(f"      记录ID {record.id}: 引用车辆ID {record.bike_id} (不存在)")
        else:
            print(f"   ✅ 所有记录的车辆ID都有效")

        # 检查是否有无效的用户ID
        invalid_user_records = []
        for record in all_records:
            if record.bike_id:
                vehicle = Bikes.query.get(record.bike_id)
                if vehicle and vehicle.belong_to:
                    user = Users.query.get(vehicle.belong_to)
                    if not user:
                        invalid_user_records.append((record, vehicle))

        if invalid_user_records:
            print(f"   ⚠️  发现 {len(invalid_user_records)} 条记录的车辆所有者不存在:")
            for record, vehicle in invalid_user_records:
                print(f"      记录ID {record.id}: 车辆 {vehicle.b_num} 的所有者ID {vehicle.belong_to} (不存在)")
        else:
            print(f"   ✅ 所有记录的车辆所有者都有效")

        # 6. 生成修复建议
        print(f"\n💡 修复建议:")

        if unknown_statuses:
            print(f"   1. 未知状态记录修复:")
            print(f"      - 检查这些记录的业务逻辑")
            print(f"      - 将未知状态统一修改为合适的状态值")
            print(f"      - 建议的状态值: 0(待审核), 1(已处理), 2(申诉中), 3(已忽略)")

        if invalid_vehicle_records:
            print(f"   2. 无效车辆ID修复:")
            print(f"      - 检查这些记录是否应该删除")
            print(f"      - 或者修复车辆ID引用")

        if invalid_user_records:
            print(f"   3. 无效用户ID修复:")
            print(f"      - 检查车辆的所有者信息")
            print(f"      - 修复用户ID引用或删除无效记录")

        if not unknown_statuses and not invalid_vehicle_records and not invalid_user_records:
            print(f"   ✅ 数据库中的违规记录状态正常，无需修复")

if __name__ == "__main__":
    check_violation_records()
