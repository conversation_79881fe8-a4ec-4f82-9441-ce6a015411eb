#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
停车场使用率调试脚本
用于检查数据库中的实际数据并分析使用率计算问题
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'api'))

from app import create_app, db
from app.parkinglots.models import ParkingLot, ParkingSpace
from app.parking_records.models import ParkingRecord
from sqlalchemy import func
from datetime import datetime

def debug_parking_utilization():
    """调试停车场使用率计算"""
    app = create_app()
    
    with app.app_context():
        print("=" * 80)
        print("停车场使用率调试分析")
        print("=" * 80)
        
        # 1. 查询所有停车场
        parking_lots = ParkingLot.query.all()
        print(f"\n📊 数据库中共有 {len(parking_lots)} 个停车场")
        
        for lot in parking_lots:
            print(f"\n🏢 停车场: {lot.name} (ID: {lot.id})")
            print("-" * 50)
            
            # 2. 查询停车场的车位信息
            total_spaces = ParkingSpace.query.filter_by(parking_lot_id=lot.id).count()
            occupied_spaces = ParkingSpace.query.filter_by(parking_lot_id=lot.id, status=1).count()
            available_spaces = ParkingSpace.query.filter_by(parking_lot_id=lot.id, status=0).count()
            maintenance_spaces = ParkingSpace.query.filter_by(parking_lot_id=lot.id, status=2).count()
            
            print(f"📍 车位统计 (从 parking_spaces 表):")
            print(f"   总车位数: {total_spaces}")
            print(f"   已占用: {occupied_spaces}")
            print(f"   空闲: {available_spaces}")
            print(f"   维护中: {maintenance_spaces}")
            
            # 3. 查询停车场表中存储的数据
            print(f"\n📍 停车场表存储的数据:")
            print(f"   total_spaces: {lot.total_spaces}")
            print(f"   occupied_spaces: {lot.occupied_spaces}")
            
            # 4. 查询活跃停车记录
            active_records = ParkingRecord.query.filter_by(
                parking_lot_id=lot.id,
                status=0  # 进行中
            ).count()
            
            completed_records = ParkingRecord.query.filter_by(
                parking_lot_id=lot.id,
                status=1  # 已完成
            ).count()
            
            total_records = ParkingRecord.query.filter_by(
                parking_lot_id=lot.id
            ).count()
            
            print(f"\n📍 停车记录统计:")
            print(f"   活跃记录数: {active_records}")
            print(f"   已完成记录数: {completed_records}")
            print(f"   总记录数: {total_records}")
            
            # 5. 计算不同方法的使用率
            print(f"\n📊 使用率计算对比:")
            
            # 方法1: 基于车位状态 (前端常用)
            utilization_by_spaces = round((occupied_spaces / total_spaces * 100), 2) if total_spaces > 0 else 0
            print(f"   方法1 (车位状态): {occupied_spaces}/{total_spaces} = {utilization_by_spaces}%")
            
            # 方法2: 基于停车场表数据
            utilization_by_lot_data = round((lot.occupied_spaces / lot.total_spaces * 100), 2) if lot.total_spaces > 0 else 0
            print(f"   方法2 (停车场表): {lot.occupied_spaces}/{lot.total_spaces} = {utilization_by_lot_data}%")
            
            # 方法3: 基于活跃停车记录 (后端API使用)
            utilization_by_records = round((active_records / total_spaces * 100), 2) if total_spaces > 0 else 0
            print(f"   方法3 (活跃记录): {active_records}/{total_spaces} = {utilization_by_records}%")
            
            # 6. 数据一致性检查
            print(f"\n🔍 数据一致性检查:")
            spaces_consistent = (occupied_spaces == lot.occupied_spaces)
            total_consistent = (total_spaces == lot.total_spaces)
            records_consistent = (active_records == occupied_spaces)
            
            print(f"   车位占用数据一致: {spaces_consistent} ({occupied_spaces} vs {lot.occupied_spaces})")
            print(f"   总车位数据一致: {total_consistent} ({total_spaces} vs {lot.total_spaces})")
            print(f"   记录与车位一致: {records_consistent} ({active_records} vs {occupied_spaces})")
            
            # 7. 查询具体的车位详情
            if occupied_spaces > 0:
                print(f"\n🚗 已占用车位详情:")
                occupied_space_details = ParkingSpace.query.filter_by(
                    parking_lot_id=lot.id, 
                    status=1
                ).all()
                
                for space in occupied_space_details[:5]:  # 只显示前5个
                    print(f"   车位 {space.space_number}: 车辆ID={space.current_vehicle_id}")
                
                if len(occupied_space_details) > 5:
                    print(f"   ... 还有 {len(occupied_space_details) - 5} 个已占用车位")
            
            # 8. 查询活跃停车记录详情
            if active_records > 0:
                print(f"\n📝 活跃停车记录详情:")
                active_record_details = ParkingRecord.query.filter_by(
                    parking_lot_id=lot.id,
                    status=0
                ).all()
                
                for record in active_record_details[:5]:  # 只显示前5个
                    print(f"   记录ID {record.id}: 车辆ID={record.vehicle_id}, 车位ID={record.parking_space_id}, 入场时间={record.entry_time}")
                
                if len(active_record_details) > 5:
                    print(f"   ... 还有 {len(active_record_details) - 5} 个活跃记录")
        
        # 9. 总结分析
        print(f"\n" + "=" * 80)
        print("🔍 问题分析总结")
        print("=" * 80)
        
        print("\n可能的问题原因:")
        print("1. 数据不同步: 停车场表中的 occupied_spaces 与实际车位状态不一致")
        print("2. 计算方法不同: 前端和后端使用不同的计算方法")
        print("3. 数据更新延迟: 车位状态更新后，停车场统计数据未及时更新")
        print("4. 停车记录与车位状态不同步: 停车记录状态与车位占用状态不一致")
        
        print("\n建议解决方案:")
        print("1. 统一使用车位状态计算使用率")
        print("2. 添加数据同步机制，确保停车场表数据实时更新")
        print("3. 修复后端API使用活跃记录计算使用率的逻辑")
        print("4. 添加数据一致性检查和修复功能")

if __name__ == "__main__":
    debug_parking_utilization()
