#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试停车记录API返回的数据结构
检查为什么前端显示"未知停车场"
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'api'))

from app import create_app, db
from app.parking_records.models import ParkingRecord
from app.parkinglots.models import ParkingLot, ParkingSpace
from app.bikes.models import Bikes
from app.users.models import Users
import json

def debug_parking_records_api():
    """调试停车记录API数据结构"""
    app = create_app()
    
    with app.app_context():
        print("=" * 80)
        print("停车记录API数据结构调试")
        print("=" * 80)
        
        # 1. 检查数据库中的停车记录
        print("\n🔍 检查数据库中的停车记录")
        print("-" * 60)
        
        records = ParkingRecord.query.order_by(ParkingRecord.entry_time.desc()).limit(5).all()
        print(f"找到 {len(records)} 条最新停车记录")
        
        for i, record in enumerate(records, 1):
            print(f"\n📋 记录 {i} (ID: {record.id}):")
            print(f"   用户ID: {record.user_id}")
            print(f"   车辆ID: {record.vehicle_id}")
            print(f"   停车场ID: {record.parking_lot_id}")
            print(f"   车位ID: {record.parking_space_id}")
            print(f"   状态: {record.status}")
            print(f"   进入时间: {record.entry_time}")
            print(f"   退出时间: {record.exit_time}")
            
            # 检查关联数据
            parking_lot = ParkingLot.query.get(record.parking_lot_id)
            parking_space = ParkingSpace.query.get(record.parking_space_id)
            vehicle = Bikes.query.get(record.vehicle_id)
            user = Users.query.get(record.user_id)
            
            print(f"   停车场: {parking_lot.name if parking_lot else '❌ 不存在'}")
            print(f"   车位: {parking_space.space_number if parking_space else '❌ 不存在'}")
            print(f"   车辆: {vehicle.b_num if vehicle else '❌ 不存在'}")
            print(f"   用户: {user.u_name if user else '❌ 不存在'}")
        
        # 2. 测试 get_details 方法（不包含关联）
        print(f"\n🔍 测试 get_details 方法（不包含关联）")
        print("-" * 60)
        
        if records:
            record = records[0]
            details_basic = record.get_details(include_relations=False)
            print(f"基础详情数据:")
            print(json.dumps(details_basic, indent=2, ensure_ascii=False, default=str))
        
        # 3. 测试 get_details 方法（包含关联）
        print(f"\n🔍 测试 get_details 方法（包含关联）")
        print("-" * 60)
        
        if records:
            record = records[0]
            details_full = record.get_details(include_relations=True)
            print(f"完整详情数据:")
            print(json.dumps(details_full, indent=2, ensure_ascii=False, default=str))
        
        # 4. 分析前端期望的数据结构
        print(f"\n🔍 分析前端期望的数据结构")
        print("-" * 60)
        
        print("前端Dashboard期望的字段:")
        print("- parking_lot_name: 停车场名称")
        print("- parking_space_number: 车位编号")
        print("- status: 状态")
        print("- entry_time: 进入时间")
        
        print("\n后端API实际返回的字段:")
        if records:
            record = records[0]
            details = record.get_details(include_relations=True)
            print("- parking_lot: 停车场对象 (包含 id, name, address)")
            print("- parking_space: 车位对象 (包含 id, number, space_number, type, type_text)")
            print("- parking_space_number: 车位编号 (根级字段)")
            print("- status: 状态")
            print("- entry_time: 进入时间")
            
            print(f"\n❌ 问题发现:")
            print(f"前端期望: parking_lot_name")
            print(f"后端返回: parking_lot.name")
            print(f"前端无法直接访问嵌套的 parking_lot.name 字段!")
        
        # 5. 检查用户停车记录API
        print(f"\n🔍 检查用户停车记录API")
        print("-" * 60)
        
        # 模拟API调用
        user_id = 1  # admin用户
        user_records = ParkingRecord.query.filter_by(user_id=user_id).order_by(ParkingRecord.entry_time.desc()).limit(5).all()
        
        print(f"用户 {user_id} 的停车记录:")
        api_response_data = []
        
        for record in user_records:
            record_data = record.get_details(include_relations=True)
            api_response_data.append(record_data)
            
            # 检查前端需要的字段是否存在
            has_parking_lot_name = 'parking_lot_name' in record_data
            has_parking_lot_obj = 'parking_lot' in record_data and record_data['parking_lot'] is not None
            
            print(f"\n记录 ID {record.id}:")
            print(f"   parking_lot_name 字段: {'✅ 存在' if has_parking_lot_name else '❌ 不存在'}")
            print(f"   parking_lot 对象: {'✅ 存在' if has_parking_lot_obj else '❌ 不存在'}")
            
            if has_parking_lot_obj:
                print(f"   停车场名称: {record_data['parking_lot']['name']}")
            
        # 6. 提供修复方案
        print(f"\n💡 修复方案")
        print("-" * 60)
        
        print("方案1: 修改后端API，在根级添加 parking_lot_name 字段")
        print("方案2: 修改前端代码，使用 parking_lot.name 访问停车场名称")
        print("方案3: 在后端API中同时提供两种字段格式")
        
        print(f"\n推荐方案1: 修改后端API")
        print("在 get_details 方法中添加:")
        print("data['parking_lot_name'] = parking_lot.name if parking_lot else '未知停车场'")

if __name__ == "__main__":
    debug_parking_records_api()
