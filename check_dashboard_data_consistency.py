#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Dashboard页面数据一致性
重点检查"我的最新停车记录"和"系统公告"模块
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'api'))

from app import create_app, db
from app.parking_records.models import ParkingRecord
from app.parkinglots.models import ParkingLot, ParkingSpace
from app.bikes.models import Bikes
from app.users.models import Users
from sqlalchemy import func, and_
from datetime import datetime, timedelta

def check_dashboard_data_consistency():
    """检查Dashboard页面数据一致性"""
    app = create_app()
    
    with app.app_context():
        print("=" * 80)
        print("Dashboard页面数据一致性检查")
        print("=" * 80)
        
        # 1. 检查"我的最新停车记录"模块
        print("\n🔍 检查 '我的最新停车记录' 模块")
        print("-" * 60)
        
        check_recent_parking_records()
        
        # 2. 检查"系统公告"模块
        print("\n🔍 检查 '系统公告' 模块")
        print("-" * 60)
        
        check_system_announcements()
        
        # 3. 检查停车记录状态一致性
        print("\n🔍 检查停车记录状态一致性")
        print("-" * 60)
        
        check_parking_record_status_consistency()
        
        # 4. 检查数据关联完整性
        print("\n🔍 检查数据关联完整性")
        print("-" * 60)
        
        check_data_relationship_integrity()

def check_recent_parking_records():
    """检查最新停车记录的数据一致性"""
    
    # 获取所有用户
    users = Users.query.all()
    print(f"📊 系统中共有 {len(users)} 个用户")
    
    for user in users:
        print(f"\n👤 用户: {user.u_name} (ID: {user.u_id})")
        
        # 模拟前端API调用：获取用户最新5条停车记录
        recent_records = ParkingRecord.query.filter_by(
            user_id=user.u_id
        ).order_by(ParkingRecord.entry_time.desc()).limit(5).all()
        
        print(f"   最新停车记录数: {len(recent_records)}")
        
        if recent_records:
            for i, record in enumerate(recent_records, 1):
                # 检查记录的完整性
                issues = []
                
                # 检查停车场信息
                parking_lot = ParkingLot.query.get(record.parking_lot_id)
                if not parking_lot:
                    issues.append(f"停车场ID {record.parking_lot_id} 不存在")
                
                # 检查车位信息
                parking_space = ParkingSpace.query.get(record.parking_space_id)
                if not parking_space:
                    issues.append(f"车位ID {record.parking_space_id} 不存在")
                
                # 检查车辆信息
                vehicle = Bikes.query.get(record.vehicle_id)
                if not vehicle:
                    issues.append(f"车辆ID {record.vehicle_id} 不存在")
                
                # 检查状态一致性
                status_text = get_record_status_text(record.status)
                if status_text == "未知":
                    issues.append(f"未知状态值: {record.status}")
                
                # 检查时间逻辑
                if record.exit_time and record.entry_time:
                    if record.exit_time < record.entry_time:
                        issues.append("退出时间早于进入时间")
                
                # 输出记录信息
                print(f"   记录 {i}:")
                print(f"      ID: {record.id}")
                print(f"      状态: {record.status} ({status_text})")
                print(f"      停车场: {parking_lot.name if parking_lot else '未知'}")
                print(f"      车位: {parking_space.space_number if parking_space else '未知'}")
                print(f"      车辆: {vehicle.b_num if vehicle else '未知'}")
                print(f"      进入时间: {record.entry_time}")
                print(f"      退出时间: {record.exit_time or '进行中'}")
                
                if issues:
                    print(f"      ⚠️  数据问题: {'; '.join(issues)}")
                else:
                    print(f"      ✅ 数据完整")

def check_system_announcements():
    """检查系统公告模块"""
    
    print("📢 系统公告模块分析:")
    print("   当前实现: 前端硬编码的静态公告数据")
    print("   数据来源: Vue组件的data属性")
    
    # 前端硬编码的公告数据
    static_announcements = [
        {
            'title': '系统维护通知',
            'time': '2024-01-15',
            'type': 'warning'
        },
        {
            'title': '新增充电桩投入使用',
            'time': '2024-01-10',
            'type': 'success'
        },
        {
            'title': '停车规则更新',
            'time': '2024-01-05',
            'type': 'info'
        }
    ]
    
    print(f"\n   静态公告数量: {len(static_announcements)}")
    for i, announcement in enumerate(static_announcements, 1):
        print(f"   公告 {i}: {announcement['title']} ({announcement['time']})")
    
    print("\n   ⚠️  潜在问题:")
    print("   1. 公告数据是硬编码的，无法动态更新")
    print("   2. 所有用户看到相同的公告，无法个性化")
    print("   3. 公告时间是固定的，可能显示过期信息")
    print("   4. 没有后端API支持，无法进行公告管理")
    
    print("\n   💡 建议改进:")
    print("   1. 创建公告管理后端API")
    print("   2. 支持动态公告发布和管理")
    print("   3. 支持公告的有效期设置")
    print("   4. 支持按用户角色显示不同公告")

def check_parking_record_status_consistency():
    """检查停车记录状态一致性"""
    
    # 前端状态映射（来自dashboard/index.vue）
    frontend_status_map = {
        0: '进行中',
        1: '已结束',
        2: '异常'
    }
    
    # 后端状态定义（来自constants.py）
    backend_status_map = {
        0: '进行中',    # ACTIVE
        1: '已完成',    # COMPLETED
        2: '异常'       # ABNORMAL
    }
    
    print("📊 停车记录状态映射对比:")
    print("\n   前端状态映射 (dashboard/index.vue):")
    for status, text in frontend_status_map.items():
        print(f"      {status}: {text}")
    
    print("\n   后端状态映射 (constants.py):")
    for status, text in backend_status_map.items():
        print(f"      {status}: {text}")
    
    # 检查不一致
    inconsistencies = []
    for status in frontend_status_map:
        if status in backend_status_map:
            if frontend_status_map[status] != backend_status_map[status]:
                inconsistencies.append(f"状态 {status}: 前端='{frontend_status_map[status]}' vs 后端='{backend_status_map[status]}'")
    
    if inconsistencies:
        print(f"\n   ⚠️  发现状态映射不一致:")
        for inconsistency in inconsistencies:
            print(f"      {inconsistency}")
    else:
        print(f"\n   ✅ 状态映射基本一致")
    
    # 检查实际数据库中的状态分布
    status_stats = db.session.query(
        ParkingRecord.status,
        func.count(ParkingRecord.id).label('count')
    ).group_by(ParkingRecord.status).all()
    
    print(f"\n   📈 数据库中的状态分布:")
    for status, count in status_stats:
        frontend_text = frontend_status_map.get(status, '未知')
        backend_text = backend_status_map.get(status, '未知')
        print(f"      状态 {status}: {count} 条记录")
        print(f"         前端显示: {frontend_text}")
        print(f"         后端定义: {backend_text}")
        if frontend_text != backend_text:
            print(f"         ⚠️  显示不一致!")

def check_data_relationship_integrity():
    """检查数据关联完整性"""
    
    print("🔗 数据关联完整性检查:")
    
    # 检查停车记录的关联数据
    all_records = ParkingRecord.query.all()
    print(f"\n   总停车记录数: {len(all_records)}")
    
    # 统计各种关联问题
    missing_parking_lots = 0
    missing_parking_spaces = 0
    missing_vehicles = 0
    missing_users = 0
    status_inconsistencies = 0
    
    for record in all_records:
        # 检查停车场
        if not ParkingLot.query.get(record.parking_lot_id):
            missing_parking_lots += 1
        
        # 检查车位
        if not ParkingSpace.query.get(record.parking_space_id):
            missing_parking_spaces += 1
        
        # 检查车辆
        if not Bikes.query.get(record.vehicle_id):
            missing_vehicles += 1
        
        # 检查用户
        if not Users.query.get(record.user_id):
            missing_users += 1
        
        # 检查状态一致性（如果是进行中的记录，车位应该被占用）
        if record.status == 0:  # 进行中
            space = ParkingSpace.query.get(record.parking_space_id)
            if space and space.status != 1:  # 车位状态不是已占用
                status_inconsistencies += 1
    
    print(f"\n   关联完整性统计:")
    print(f"      缺失停车场引用: {missing_parking_lots} 条记录")
    print(f"      缺失车位引用: {missing_parking_spaces} 条记录")
    print(f"      缺失车辆引用: {missing_vehicles} 条记录")
    print(f"      缺失用户引用: {missing_users} 条记录")
    print(f"      状态不一致: {status_inconsistencies} 条记录")
    
    total_issues = missing_parking_lots + missing_parking_spaces + missing_vehicles + missing_users + status_inconsistencies
    
    if total_issues == 0:
        print(f"\n   ✅ 数据关联完整性良好")
    else:
        print(f"\n   ⚠️  发现 {total_issues} 个数据关联问题")

def get_record_status_text(status):
    """获取停车记录状态文本"""
    status_map = {
        0: '进行中',
        1: '已结束',
        2: '异常'
    }
    return status_map.get(status, '未知')

if __name__ == "__main__":
    check_dashboard_data_consistency()
