#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
初始化公告数据
创建公告表并插入默认公告数据
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'api'))

from app import create_app, db
from datetime import datetime, timedelta

def init_announcements():
    """初始化公告数据"""
    app = create_app()

    with app.app_context():
        print("=" * 60)
        print("初始化公告系统")
        print("=" * 60)

        # 1. 创建公告表（如果不存在）
        try:
            # 检查表是否存在
            from sqlalchemy import text
            result = db.session.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='announcements'"))
            table_exists = result.fetchone() is not None

            if not table_exists:
                print("📝 创建公告表...")

                # 创建公告表
                create_table_sql = """
                CREATE TABLE announcements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title VARCHAR(200) NOT NULL,
                    content TEXT NOT NULL,
                    type VARCHAR(20) NOT NULL DEFAULT 'info',
                    priority INTEGER NOT NULL DEFAULT 1,
                    target_roles VARCHAR(100),
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    start_time DATETIME,
                    end_time DATETIME,
                    created_by INTEGER NOT NULL,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (created_by) REFERENCES users (u_id)
                )
                """

                db.session.execute(text(create_table_sql))
                db.session.commit()
                print("✅ 公告表创建成功")
            else:
                print("📋 公告表已存在")

                # 检查是否需要添加新字段
                columns_to_add = [
                    ('priority', 'INTEGER NOT NULL DEFAULT 1'),
                    ('target_roles', 'VARCHAR(100)'),
                    ('is_active', 'BOOLEAN NOT NULL DEFAULT 1'),
                    ('start_time', 'DATETIME'),
                    ('end_time', 'DATETIME')
                ]

                for column_name, column_def in columns_to_add:
                    try:
                        # 检查字段是否存在
                        result = db.session.execute(text(f"PRAGMA table_info(announcements)"))
                        columns = [row[1] for row in result.fetchall()]

                        if column_name not in columns:
                            print(f"📝 添加字段: {column_name}")
                            db.session.execute(text(f"ALTER TABLE announcements ADD COLUMN {column_name} {column_def}"))
                            db.session.commit()
                            print(f"✅ 字段 {column_name} 添加成功")
                    except Exception as e:
                        print(f"⚠️  添加字段 {column_name} 失败: {str(e)}")

        except Exception as e:
            print(f"❌ 创建公告表失败: {str(e)}")
            return False

        # 2. 插入默认公告数据
        try:
            print("\n📢 插入默认公告数据...")

            # 检查是否已有公告数据
            result = db.session.execute(text("SELECT COUNT(*) FROM announcements"))
            count = result.fetchone()[0]

            if count == 0:
                # 插入默认公告
                default_announcements = [
                    {
                        'title': '欢迎使用校园电车管理系统',
                        'content': '系统已正式上线，为您提供便捷的电车停车管理服务。请各位用户遵守停车规则，文明停车。',
                        'type': 'success',
                        'priority': 3,
                        'target_roles': None,
                        'is_active': 1,
                        'created_by': 1,
                        'created_at': datetime.now(),
                        'updated_at': datetime.now()
                    },
                    {
                        'title': '停车规则更新通知',
                        'content': '为了更好地管理校园停车秩序，系统停车规则已更新。请用户及时查看最新的停车规则和收费标准。',
                        'type': 'info',
                        'priority': 2,
                        'target_roles': None,
                        'is_active': 1,
                        'created_by': 1,
                        'created_at': datetime.now() - timedelta(days=1),
                        'updated_at': datetime.now() - timedelta(days=1)
                    },
                    {
                        'title': '管理员操作指南',
                        'content': '管理员用户请注意：系统提供了完整的车辆管理、停车场管理、违规管理等功能。请合理使用管理权限。',
                        'type': 'warning',
                        'priority': 2,
                        'target_roles': 'admin',
                        'is_active': 1,
                        'created_by': 1,
                        'created_at': datetime.now() - timedelta(days=2),
                        'updated_at': datetime.now() - timedelta(days=2)
                    },
                    {
                        'title': '保安用户权限说明',
                        'content': '保安用户可以查看停车统计、违规记录等信息，协助维护校园停车秩序。如有疑问请联系系统管理员。',
                        'type': 'info',
                        'priority': 1,
                        'target_roles': 'security',
                        'is_active': 1,
                        'created_by': 1,
                        'created_at': datetime.now() - timedelta(days=3),
                        'updated_at': datetime.now() - timedelta(days=3)
                    },
                    {
                        'title': '系统功能介绍',
                        'content': '本系统支持车辆注册、停车记录管理、违规处理等功能。如需帮助，请查看用户手册或联系管理员。',
                        'type': 'info',
                        'priority': 1,
                        'target_roles': 'user',
                        'is_active': 1,
                        'created_by': 1,
                        'created_at': datetime.now() - timedelta(days=4),
                        'updated_at': datetime.now() - timedelta(days=4)
                    }
                ]

                for announcement in default_announcements:
                    insert_sql = """
                    INSERT INTO announcements
                    (title, content, type, priority, target_roles, is_active, created_by, created_at, updated_at)
                    VALUES (:title, :content, :type, :priority, :target_roles, :is_active, :created_by, :created_at, :updated_at)
                    """

                    db.session.execute(text(insert_sql), {
                        'title': announcement['title'],
                        'content': announcement['content'],
                        'type': announcement['type'],
                        'priority': announcement['priority'],
                        'target_roles': announcement['target_roles'],
                        'is_active': announcement['is_active'],
                        'created_by': announcement['created_by'],
                        'created_at': announcement['created_at'],
                        'updated_at': announcement['updated_at']
                    })

                db.session.commit()
                print(f"✅ 成功插入 {len(default_announcements)} 条默认公告")
            else:
                print(f"📋 数据库中已有 {count} 条公告，跳过插入")

        except Exception as e:
            print(f"❌ 插入默认公告数据失败: {str(e)}")
            return False

        # 3. 验证数据
        try:
            print("\n🔍 验证公告数据...")
            result = db.session.execute(text("SELECT id, title, type, target_roles FROM announcements ORDER BY priority DESC, created_at DESC"))
            announcements = result.fetchall()

            print(f"📊 公告总数: {len(announcements)}")
            for announcement in announcements:
                target = announcement[3] if announcement[3] else "所有用户"
                print(f"   ID {announcement[0]}: {announcement[1]} ({announcement[2]}) - 目标: {target}")

            print("\n✅ 公告系统初始化完成!")
            return True

        except Exception as e:
            print(f"❌ 验证公告数据失败: {str(e)}")
            return False

if __name__ == "__main__":
    success = init_announcements()
    if success:
        print("\n🎉 公告系统初始化成功!")
    else:
        print("\n💥 公告系统初始化失败!")
        sys.exit(1)
