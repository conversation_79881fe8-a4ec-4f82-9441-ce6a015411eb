#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的停车场使用率API
"""

import requests
import json

def test_parking_stats_api():
    """测试停车场统计API"""
    
    # API端点
    url = "http://localhost:5000/api/parking-records/stats"
    
    # 测试参数
    params = {
        'scope': 'user'
    }
    
    # 模拟JWT token (需要替换为实际的token)
    headers = {
        'Authorization': 'Bearer your_jwt_token_here',
        'Content-Type': 'application/json'
    }
    
    try:
        print("🔍 测试停车场使用率API...")
        print(f"请求URL: {url}")
        print(f"请求参数: {params}")
        
        # 发送请求
        response = requests.get(url, params=params, headers=headers)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ API请求成功!")
            print("\n📊 停车场使用率数据:")
            
            if 'data' in data and 'parking_lots' in data['data']:
                parking_lots = data['data']['parking_lots']
                
                for lot in parking_lots:
                    print(f"\n🏢 停车场: {lot['name']} (ID: {lot['id']})")
                    print(f"   总车位数: {lot['total_spaces']}")
                    print(f"   已占用车位: {lot['occupied_spaces']}")
                    print(f"   活跃记录数: {lot['active_records']}")
                    print(f"   使用率: {lot['utilization_rate']}%")
                    
                    # 验证计算是否正确
                    expected_rate = round((lot['active_records'] / lot['total_spaces'] * 100), 2) if lot['total_spaces'] > 0 else 0
                    if abs(lot['utilization_rate'] - expected_rate) < 0.01:
                        print(f"   ✅ 使用率计算正确")
                    else:
                        print(f"   ❌ 使用率计算错误，期望: {expected_rate}%")
            else:
                print("❌ 响应数据格式不正确")
                
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器，请确保后端服务正在运行")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")

def test_without_auth():
    """测试不带认证的请求"""
    
    url = "http://localhost:5000/api/parking-records/stats"
    params = {'scope': 'user'}
    
    try:
        print("\n🔍 测试不带认证的请求...")
        response = requests.get(url, params=params)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 401:
            print("✅ 正确返回401未授权状态")
        else:
            print(f"⚠️  期望401状态码，实际返回: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")

if __name__ == "__main__":
    print("=" * 60)
    print("停车场使用率API修复验证测试")
    print("=" * 60)
    
    # 测试主要功能
    test_parking_stats_api()
    
    # 测试认证
    test_without_auth()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    
    print("\n📝 说明:")
    print("1. 如果看到连接错误，请确保后端服务正在运行 (python run.py)")
    print("2. 如果看到401错误，这是正常的，因为没有提供有效的JWT token")
    print("3. 要完整测试，需要先登录获取有效的JWT token")
