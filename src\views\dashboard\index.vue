<template>
  <div class="dashboard-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <i class="el-icon-monitor"></i>
          校园电动车管理系统仪表盘
        </h1>
        <p class="page-subtitle">欢迎回来，{{ name }}！今天是 {{ currentDate }}</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" icon="el-icon-refresh" @click="refreshData" :loading="refreshing">
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 数据概览卡片 -->
    <div v-loading="loading" class="overview-section">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :md="6" :lg="6" v-for="(item, index) in overviewCards" :key="index">
          <el-card shadow="hover" class="overview-card" :class="item.type">
            <div class="card-content">
              <div class="card-icon" :style="{ backgroundColor: item.color }">
                <i :class="item.icon"></i>
              </div>
              <div class="card-info">
                <div class="card-value">{{ item.value }}</div>
                <div class="card-label">{{ item.label }}</div>
                <div class="card-trend" v-if="item.trend">
                  <i :class="item.trend.icon" :style="{ color: item.trend.color }"></i>
                  <span :style="{ color: item.trend.color }">{{ item.trend.text }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表分析区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 停车记录趋势图 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card shadow="hover" class="chart-card">
            <div slot="header" class="chart-header">
              <span><i class="el-icon-data-line"></i> 停车记录趋势</span>
              <el-select v-model="trendPeriod" size="mini" @change="updateTrendChart">
                <el-option label="最近7天" value="week"></el-option>
                <el-option label="最近30天" value="month"></el-option>
              </el-select>
            </div>
            <div ref="trendChart" class="chart-container"></div>
          </el-card>
        </el-col>

        <!-- 停车场使用率 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card shadow="hover" class="chart-card">
            <div slot="header" class="chart-header">
              <span><i class="el-icon-pie-chart"></i> 停车场使用率</span>
            </div>
            <div ref="utilizationChart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <!-- 车辆类型分布 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card shadow="hover" class="chart-card">
            <div slot="header" class="chart-header">
              <span><i class="el-icon-bicycle"></i> 车辆类型分布</span>
            </div>
            <div ref="vehicleTypeChart" class="chart-container"></div>
          </el-card>
        </el-col>

        <!-- 违规统计 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12" v-if="isAdminOrSecurity">
          <el-card shadow="hover" class="chart-card">
            <div slot="header" class="chart-header">
              <span><i class="el-icon-warning"></i> 违规统计</span>
            </div>
            <div ref="violationChart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快捷操作区域 -->
    <div class="quick-actions-section">
      <el-card shadow="hover">
        <div slot="header" class="section-header">
          <span><i class="el-icon-s-operation"></i> 快捷操作</span>
        </div>
        <el-row :gutter="20">
          <!-- 我的车辆 -->
          <el-col :xs="12" :sm="8" :md="6" :lg="4">
            <div class="quick-action-item" @click="goToMyBikes">
              <div class="action-icon bikes">
                <i class="el-icon-bicycle"></i>
              </div>
              <div class="action-text">我的车辆</div>
            </div>
          </el-col>

          <!-- 我的停车 -->
          <el-col :xs="12" :sm="8" :md="6" :lg="4">
            <div class="quick-action-item" @click="goToMyParking">
              <div class="action-icon parking">
                <i class="el-icon-s-promotion"></i>
              </div>
              <div class="action-text">我的停车</div>
            </div>
          </el-col>

          <!-- 我的充电 -->
          <el-col :xs="12" :sm="8" :md="6" :lg="4">
            <div class="quick-action-item" @click="goToMyCharging">
              <div class="action-icon charging">
                <i class="el-icon-lightning"></i>
              </div>
              <div class="action-text">我的充电</div>
            </div>
          </el-col>

          <!-- 我的违规 -->
          <el-col :xs="12" :sm="8" :md="6" :lg="4">
            <div class="quick-action-item" @click="goToMyViolations">
              <div class="action-icon violations">
                <i class="el-icon-warning"></i>
              </div>
              <div class="action-text">我的违规</div>
            </div>
          </el-col>

          <!-- 公告栏 -->
          <el-col :xs="12" :sm="8" :md="6" :lg="4">
            <div class="quick-action-item" @click="goToAnnouncements">
              <div class="action-icon announcements">
                <i class="el-icon-bell"></i>
              </div>
              <div class="action-text">公告栏</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 最新动态 -->
    <div class="recent-activities-section">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card shadow="hover">
            <div slot="header" class="section-header">
              <span><i class="el-icon-time"></i> 我的最新停车记录</span>
              <el-button type="text" @click="goToMyParking">查看更多</el-button>
            </div>
            <div v-loading="activitiesLoading">
              <div v-if="recentParkingRecords.length === 0" class="no-data">
                <i class="el-icon-document"></i>
                <p>暂无停车记录</p>
              </div>
              <div v-else>
                <div v-for="record in recentParkingRecords" :key="record.id" class="activity-item">
                  <div class="activity-icon">
                    <i class="el-icon-s-promotion" :class="getRecordStatusClass(record.status)"></i>
                  </div>
                  <div class="activity-content">
                    <div class="activity-title">{{ record.parking_lot_name || '未知停车场' }}</div>
                    <div class="activity-desc">
                      车位：{{ record.parking_space_number || '未知' }} |
                      状态：{{ getRecordStatusText(record.status) }}
                    </div>
                    <div class="activity-time">{{ formatTime(record.entry_time) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card shadow="hover">
            <div slot="header" class="section-header">
              <span><i class="el-icon-bell"></i> 系统公告</span>
              <el-button type="text" @click="goToAnnouncements">查看更多</el-button>
            </div>
            <div class="announcements-list">
              <div v-for="(announcement, index) in systemAnnouncements" :key="index" class="announcement-item">
                <div class="announcement-icon">
                  <i class="el-icon-bell" :class="announcement.type"></i>
                </div>
                <div class="announcement-content">
                  <div class="announcement-title">{{ announcement.title }}</div>
                  <div class="announcement-time">{{ announcement.time }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import request from '@/utils/request'
import * as echarts from 'echarts'
import { getParkingStats } from '@/api/statistics'
import { getBikeStats } from '@/api/bike'
import { getViolationStatistics } from '@/api/violations'
import { getUserParkingRecords } from '@/api/parking'
import { getUserAnnouncements } from '@/api/announcements'

export default {
  name: 'Dashboard',
  data() {
    return {
      loading: true,
      refreshing: false,
      activitiesLoading: false,
      trendPeriod: 'week',

      // 概览数据
      overviewData: {
        totalVehicles: 0,
        activeParkingRecords: 0,
        todayParkingCount: 0,
        totalViolations: 0
      },

      // 图表实例
      trendChart: null,
      utilizationChart: null,
      vehicleTypeChart: null,
      violationChart: null,

      // 最新停车记录
      recentParkingRecords: [],

      // 系统公告
      systemAnnouncements: []
    }
  },
  computed: {
    ...mapGetters([
      'name',
      'roles',
      'userId'
    ]),

    // 当前日期
    currentDate() {
      return new Date().toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      })
    },

    // 是否为管理员
    isAdmin() {
      return this.roles && this.roles.includes('admin')
    },

    // 是否为管理员或保安
    isAdminOrSecurity() {
      return this.roles && (this.roles.includes('admin') || this.roles.includes('security'))
    },

    // 概览卡片数据
    overviewCards() {
      return [
        {
          label: '我的车辆',
          value: this.overviewData.totalVehicles,
          icon: 'el-icon-bicycle',
          color: '#409EFF',
          type: 'primary',
          trend: {
            icon: 'el-icon-caret-top',
            color: '#67C23A',
            text: '正常'
          }
        },
        {
          label: '进行中停车',
          value: this.overviewData.activeParkingRecords,
          icon: 'el-icon-s-promotion',
          color: '#67C23A',
          type: 'success',
          trend: {
            icon: 'el-icon-caret-top',
            color: '#67C23A',
            text: '活跃'
          }
        },
        {
          label: '今日停车次数',
          value: this.overviewData.todayParkingCount,
          icon: 'el-icon-data-line',
          color: '#E6A23C',
          type: 'warning',
          trend: {
            icon: 'el-icon-caret-top',
            color: '#67C23A',
            text: '+12%'
          }
        },
        {
          label: this.isAdminOrSecurity ? '违规记录' : '我的违规',
          value: this.overviewData.totalViolations,
          icon: 'el-icon-warning',
          color: '#F56C6C',
          type: 'danger',
          trend: {
            icon: 'el-icon-caret-bottom',
            color: '#67C23A',
            text: '减少'
          }
        }
      ]
    }
  },

  mounted() {
    this.initDashboard()
  },

  beforeDestroy() {
    // 销毁图表实例
    if (this.trendChart) {
      this.trendChart.dispose()
    }
    if (this.utilizationChart) {
      this.utilizationChart.dispose()
    }
    if (this.vehicleTypeChart) {
      this.vehicleTypeChart.dispose()
    }
    if (this.violationChart) {
      this.violationChart.dispose()
    }
  },

  methods: {
    // 初始化仪表盘
    async initDashboard() {
      this.loading = true
      try {
        await Promise.all([
          this.fetchOverviewData(),
          this.fetchRecentParkingRecords(),
          this.fetchSystemAnnouncements()
        ])

        // 等待DOM更新后初始化图表
        this.$nextTick(() => {
          this.initCharts()
        })
      } catch (error) {
        console.error('初始化仪表盘失败:', error)
        this.$message.error('加载仪表盘数据失败')
      } finally {
        this.loading = false
      }
    },

    // 刷新数据
    async refreshData() {
      this.refreshing = true
      try {
        await this.initDashboard()
        this.$message.success('数据刷新成功')
      } catch (error) {
        this.$message.error('数据刷新失败')
      } finally {
        this.refreshing = false
      }
    },

    // 获取概览数据
    async fetchOverviewData() {
      try {
        // 获取车辆统计
        const bikeStatsResponse = await getBikeStats()
        if (bikeStatsResponse.code === 20000) {
          this.overviewData.totalVehicles = bikeStatsResponse.data.total || 0
        }

        // 获取停车统计
        const parkingStatsResponse = await getParkingStats({ scope: 'user' })
        if (parkingStatsResponse.code === 20000) {
          const data = parkingStatsResponse.data
          this.overviewData.activeParkingRecords = data.overview?.active_records || 0
          this.overviewData.todayParkingCount = data.overview?.today_records || 0
        }

        // 如果是管理员或保安，获取违规统计
        if (this.isAdminOrSecurity) {
          try {
            const violationStatsResponse = await getViolationStatistics()
            if (violationStatsResponse.code === 20000) {
              this.overviewData.totalViolations = violationStatsResponse.data.total || 0
            }
          } catch (error) {
            console.warn('获取违规统计失败:', error)
            this.overviewData.totalViolations = 0
          }
        }
      } catch (error) {
        console.error('获取概览数据失败:', error)
        throw error
      }
    },

    // 获取最新停车记录
    async fetchRecentParkingRecords() {
      this.activitiesLoading = true
      try {
        const response = await getUserParkingRecords({
          page: 1,
          per_page: 5,
          sort: 'entry_time',
          order: 'desc'
        })

        if (response.code === 20000 && response.data?.items) {
          this.recentParkingRecords = response.data.items
        }
      } catch (error) {
        console.error('获取最新停车记录失败:', error)
      } finally {
        this.activitiesLoading = false
      }
    },

    // 获取系统公告
    async fetchSystemAnnouncements() {
      try {
        const response = await getUserAnnouncements({
          limit: 5
        })

        if (response.code === 20000 && response.data?.items) {
          this.systemAnnouncements = response.data.items.map(announcement => ({
            title: announcement.title,
            time: this.formatAnnouncementTime(announcement.created_at),
            type: announcement.type,
            content: announcement.content
          }))
        }
      } catch (error) {
        console.error('获取系统公告失败:', error)
        // 如果获取失败，使用默认公告
        this.systemAnnouncements = [
          {
            title: '欢迎使用校园电车管理系统',
            time: this.formatAnnouncementTime(new Date().toISOString()),
            type: 'success'
          }
        ]
      }
    },

    // 初始化图表
    initCharts() {
      this.initTrendChart()
      this.initUtilizationChart()
      this.initVehicleTypeChart()
      if (this.isAdminOrSecurity) {
        this.initViolationChart()
      }
    },

    // 初始化趋势图表
    async initTrendChart() {
      if (!this.$refs.trendChart) return

      this.trendChart = echarts.init(this.$refs.trendChart)
      await this.updateTrendChart()
    },

    // 更新趋势图表
    async updateTrendChart() {
      if (!this.trendChart) return

      try {
        const response = await getParkingStats({
          date_range: this.trendPeriod,
          scope: 'user'
        })

        if (response.code === 20000 && response.data?.daily_stats) {
          const dailyStats = response.data.daily_stats
          const dates = dailyStats.map(item => item.date)
          const counts = dailyStats.map(item => item.count)

          const option = {
            title: {
              text: '停车记录趋势',
              textStyle: { fontSize: 14, color: '#333' }
            },
            tooltip: {
              trigger: 'axis',
              formatter: '{b}<br/>{a}: {c} 次'
            },
            xAxis: {
              type: 'category',
              data: dates,
              axisLabel: { rotate: 0 }
            },
            yAxis: {
              type: 'value',
              name: '停车次数'
            },
            series: [{
              name: '停车次数',
              type: 'line',
              data: counts,
              smooth: true,
              itemStyle: { color: '#409EFF' },
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0, y: 0, x2: 0, y2: 1,
                  colorStops: [
                    { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
                    { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
                  ]
                }
              }
            }]
          }

          this.trendChart.setOption(option)
        }
      } catch (error) {
        console.error('更新趋势图表失败:', error)
      }
    },

    // 初始化使用率图表
    async initUtilizationChart() {
      if (!this.$refs.utilizationChart) return

      this.utilizationChart = echarts.init(this.$refs.utilizationChart)

      try {
        const response = await getParkingStats({ scope: 'user' })

        if (response.code === 20000 && response.data?.parking_lots) {
          const lots = response.data.parking_lots
          const data = lots.map(lot => ({
            name: lot.name,
            value: lot.utilization_rate || 0
          }))

          const option = {
            title: {
              text: '停车场使用率',
              textStyle: { fontSize: 14, color: '#333' }
            },
            tooltip: {
              trigger: 'item',
              formatter: '{a}<br/>{b}: {c}%'
            },
            series: [{
              name: '使用率',
              type: 'pie',
              radius: ['40%', '70%'],
              data: data,
              itemStyle: {
                borderRadius: 5,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: true,
                formatter: '{b}: {c}%'
              }
            }]
          }

          this.utilizationChart.setOption(option)
        }
      } catch (error) {
        console.error('初始化使用率图表失败:', error)
      }
    },

    // 初始化车辆类型图表
    async initVehicleTypeChart() {
      if (!this.$refs.vehicleTypeChart) return

      this.vehicleTypeChart = echarts.init(this.$refs.vehicleTypeChart)

      try {
        const response = await getBikeStats()

        if (response.code === 20000 && response.data?.by_type) {
          const typeData = response.data.by_type
          const data = Object.keys(typeData).map(type => ({
            name: type,
            value: typeData[type]
          }))

          const option = {
            title: {
              text: '车辆类型分布',
              textStyle: { fontSize: 14, color: '#333' }
            },
            tooltip: {
              trigger: 'item',
              formatter: '{a}<br/>{b}: {c} ({d}%)'
            },
            series: [{
              name: '车辆类型',
              type: 'pie',
              radius: '60%',
              data: data,
              itemStyle: {
                borderRadius: 5,
                borderColor: '#fff',
                borderWidth: 2
              }
            }]
          }

          this.vehicleTypeChart.setOption(option)
        }
      } catch (error) {
        console.error('初始化车辆类型图表失败:', error)
      }
    },

    // 初始化违规图表
    async initViolationChart() {
      if (!this.$refs.violationChart || !this.isAdminOrSecurity) return

      this.violationChart = echarts.init(this.$refs.violationChart)

      try {
        const response = await getViolationStatistics()

        if (response.code === 20000 && response.data?.status) {
          const statusData = response.data.status
          const data = Object.keys(statusData).map(status => ({
            name: this.getViolationStatusText(status),
            value: statusData[status]
          }))

          const option = {
            title: {
              text: '违规状态分布',
              textStyle: { fontSize: 14, color: '#333' }
            },
            tooltip: {
              trigger: 'item',
              formatter: '{a}<br/>{b}: {c} ({d}%)'
            },
            series: [{
              name: '违规状态',
              type: 'pie',
              radius: '60%',
              data: data,
              itemStyle: {
                borderRadius: 5,
                borderColor: '#fff',
                borderWidth: 2
              }
            }]
          }

          this.violationChart.setOption(option)
        }
      } catch (error) {
        console.error('初始化违规图表失败:', error)
      }
    },

    // 导航方法
    goToMyBikes() {
      this.$router.push('/profile/mybikes')
    },

    goToMyParking() {
      this.$router.push('/profile/myparking')
    },

    goToMyCharging() {
      this.$router.push('/profile/charging')
    },

    goToMyViolations() {
      this.$router.push('/profile/violations')
    },

    goToAnnouncements() {
      this.$router.push('/announcements')
    },

    // 工具方法
    formatTime(timeStr) {
      if (!timeStr) return '-'
      const date = new Date(timeStr)
      return date.toLocaleString('zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    formatAnnouncementTime(timeStr) {
      if (!timeStr) return '-'
      const date = new Date(timeStr)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    },

    getRecordStatusText(status) {
      const statusMap = {
        0: '进行中',
        1: '已完成',
        2: '异常'
      }
      return statusMap[status] || '未知'
    },

    getRecordStatusClass(status) {
      const classMap = {
        0: 'status-active',
        1: 'status-completed',
        2: 'status-abnormal'
      }
      return classMap[status] || ''
    },

    getViolationStatusText(status) {
      const statusMap = {
        0: '待审核',
        1: '已处理',
        2: '申诉中',
        3: '已忽略'
      }
      return statusMap[status] || '未知'
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

// 页面标题
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;

  .header-content {
    .page-title {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;

      i {
        margin-right: 12px;
        font-size: 32px;
      }
    }

    .page-subtitle {
      margin: 0;
      font-size: 16px;
      opacity: 0.9;
    }
  }

  .header-actions {
    .el-button {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
      }
    }
  }
}

// 概览卡片
.overview-section {
  margin-bottom: 30px;

  .overview-card {
    border: none;
    border-radius: 12px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .card-content {
      display: flex;
      align-items: center;
      padding: 10px 0;

      .card-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;

        i {
          font-size: 28px;
          color: white;
        }
      }

      .card-info {
        flex: 1;

        .card-value {
          font-size: 32px;
          font-weight: 700;
          color: #2c3e50;
          line-height: 1;
          margin-bottom: 4px;
        }

        .card-label {
          font-size: 14px;
          color: #7f8c8d;
          margin-bottom: 8px;
        }

        .card-trend {
          font-size: 12px;

          i {
            margin-right: 4px;
          }
        }
      }
    }
  }
}

// 图表区域
.charts-section {
  margin-bottom: 30px;

  .chart-card {
    border: none;
    border-radius: 12px;

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-weight: 600;
        color: #2c3e50;

        i {
          margin-right: 8px;
          color: #409EFF;
        }
      }
    }

    .chart-container {
      height: 300px;
      width: 100%;
    }
  }
}

// 快捷操作
.quick-actions-section {
  margin-bottom: 30px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      font-weight: 600;
      color: #2c3e50;

      i {
        margin-right: 8px;
        color: #409EFF;
      }
    }
  }

  .quick-action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border-radius: 12px;
    background: white;
    border: 2px solid #f0f2f5;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 120px;
    justify-content: center;

    &:hover {
      border-color: #409EFF;
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(64, 158, 255, 0.15);
    }

    .action-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 12px;

      i {
        font-size: 24px;
        color: white;
      }

      &.parking {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      &.bikes {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }

      &.charging {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      &.management {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }

      &.violations {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
      }

      &.announcements {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
      }
    }

    .action-text {
      font-size: 14px;
      font-weight: 500;
      color: #2c3e50;
      text-align: center;
    }
  }
}

// 最新动态
.recent-activities-section {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      font-weight: 600;
      color: #2c3e50;

      i {
        margin-right: 8px;
        color: #409EFF;
      }
    }

    .el-button {
      color: #409EFF;
      padding: 0;

      &:hover {
        color: #66b1ff;
      }
    }
  }

  .no-data {
    text-align: center;
    padding: 40px 20px;
    color: #909399;

    i {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  .activity-item {
    display: flex;
    align-items: flex-start;
    padding: 16px 0;
    border-bottom: 1px solid #f0f2f5;

    &:last-child {
      border-bottom: none;
    }

    .activity-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #f0f9ff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      flex-shrink: 0;

      i {
        font-size: 18px;
        color: #409EFF;

        &.status-active {
          color: #67C23A;
        }

        &.status-completed {
          color: #909399;
        }

        &.status-abnormal {
          color: #F56C6C;
        }
      }
    }

    .activity-content {
      flex: 1;

      .activity-title {
        font-size: 14px;
        font-weight: 500;
        color: #2c3e50;
        margin-bottom: 4px;
      }

      .activity-desc {
        font-size: 12px;
        color: #909399;
        margin-bottom: 4px;
      }

      .activity-time {
        font-size: 12px;
        color: #c0c4cc;
      }
    }
  }

  .announcements-list {
    .announcement-item {
      display: flex;
      align-items: flex-start;
      padding: 16px 0;
      border-bottom: 1px solid #f0f2f5;

      &:last-child {
        border-bottom: none;
      }

      .announcement-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #f0f9ff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        flex-shrink: 0;

        i {
          font-size: 18px;

          &.warning {
            color: #E6A23C;
          }

          &.success {
            color: #67C23A;
          }

          &.info {
            color: #409EFF;
          }
        }
      }

      .announcement-content {
        flex: 1;

        .announcement-title {
          font-size: 14px;
          font-weight: 500;
          color: #2c3e50;
          margin-bottom: 4px;
        }

        .announcement-time {
          font-size: 12px;
          color: #c0c4cc;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    text-align: center;

    .header-content {
      margin-bottom: 16px;

      .page-title {
        font-size: 24px;
      }
    }
  }

  .overview-section {
    .overview-card {
      margin-bottom: 16px;

      .card-content {
        .card-icon {
          width: 50px;
          height: 50px;

          i {
            font-size: 24px;
          }
        }

        .card-info {
          .card-value {
            font-size: 24px;
          }
        }
      }
    }
  }

  .quick-actions-section {
    .quick-action-item {
      height: 100px;
      padding: 15px;

      .action-icon {
        width: 40px;
        height: 40px;
        margin-bottom: 8px;

        i {
          font-size: 20px;
        }
      }

      .action-text {
        font-size: 12px;
      }
    }
  }

  .charts-section {
    .chart-container {
      height: 250px;
    }
  }
}
</style>
