#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试停车记录API修复效果
验证前端能否正确显示停车场名称
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'api'))

from app import create_app, db
from app.parking_records.models import ParkingRecord
import json

def test_parking_records_fix():
    """测试停车记录API修复效果"""
    app = create_app()
    
    with app.app_context():
        print("=" * 80)
        print("停车记录API修复效果测试")
        print("=" * 80)
        
        # 1. 测试基础 get_details 方法
        print("\n🔍 测试基础 get_details 方法（不包含关联）")
        print("-" * 60)
        
        records = ParkingRecord.query.order_by(ParkingRecord.entry_time.desc()).limit(3).all()
        
        for i, record in enumerate(records, 1):
            print(f"\n📋 记录 {i} (ID: {record.id}):")
            details = record.get_details(include_relations=False)
            
            # 检查关键字段
            has_parking_lot_name = 'parking_lot_name' in details
            parking_lot_name = details.get('parking_lot_name', '字段不存在')
            
            print(f"   ✅ parking_lot_name 字段: {'存在' if has_parking_lot_name else '❌ 不存在'}")
            print(f"   📍 停车场名称: {parking_lot_name}")
            print(f"   🚗 车位编号: {details.get('parking_space_number', '未知')}")
            print(f"   📊 状态: {details.get('status_text', '未知')}")
            print(f"   ⏰ 进入时间: {details.get('entry_time', '未知')}")
        
        # 2. 测试完整 get_details 方法
        print(f"\n🔍 测试完整 get_details 方法（包含关联）")
        print("-" * 60)
        
        for i, record in enumerate(records, 1):
            print(f"\n📋 记录 {i} (ID: {record.id}):")
            details = record.get_details(include_relations=True)
            
            # 检查关键字段
            has_parking_lot_name = 'parking_lot_name' in details
            has_parking_lot_obj = 'parking_lot' in details and details['parking_lot'] is not None
            
            parking_lot_name = details.get('parking_lot_name', '字段不存在')
            
            print(f"   ✅ parking_lot_name 字段: {'存在' if has_parking_lot_name else '❌ 不存在'}")
            print(f"   ✅ parking_lot 对象: {'存在' if has_parking_lot_obj else '❌ 不存在'}")
            print(f"   📍 停车场名称 (根级): {parking_lot_name}")
            
            if has_parking_lot_obj:
                nested_name = details['parking_lot'].get('name', '未知')
                print(f"   📍 停车场名称 (嵌套): {nested_name}")
                
                # 验证一致性
                if parking_lot_name == nested_name:
                    print(f"   ✅ 数据一致性: 根级和嵌套字段值相同")
                else:
                    print(f"   ❌ 数据一致性: 根级({parking_lot_name}) vs 嵌套({nested_name})")
        
        # 3. 模拟前端Dashboard调用
        print(f"\n🔍 模拟前端Dashboard调用")
        print("-" * 60)
        
        # 模拟 getUserParkingRecords API 调用
        user_id = 1  # admin用户
        user_records = ParkingRecord.query.filter_by(user_id=user_id).order_by(ParkingRecord.entry_time.desc()).limit(5).all()
        
        print(f"模拟API响应数据:")
        api_response = {
            'code': 20000,
            'data': {
                'items': [record.get_details(include_relations=True) for record in user_records],
                'total': len(user_records)
            },
            'message': '获取停车记录成功'
        }
        
        print(f"响应记录数: {len(api_response['data']['items'])}")
        
        # 检查每条记录的前端兼容性
        for i, item in enumerate(api_response['data']['items'], 1):
            print(f"\n前端记录 {i}:")
            
            # 模拟前端代码访问
            parking_lot_name = item.get('parking_lot_name', '未知停车场')
            parking_space_number = item.get('parking_space_number', '未知')
            status_text = item.get('status_text', '未知')
            entry_time = item.get('entry_time', '未知')
            
            print(f"   停车场: {parking_lot_name}")  # 对应前端 record.parking_lot_name
            print(f"   车位: {parking_space_number}")  # 对应前端 record.parking_space_number
            print(f"   状态: {status_text}")  # 对应前端 getRecordStatusText(record.status)
            print(f"   时间: {entry_time}")  # 对应前端 formatTime(record.entry_time)
            
            # 检查是否还会显示"未知停车场"
            if parking_lot_name == '未知停车场':
                print(f"   ⚠️  仍显示'未知停车场'，需要检查数据")
            else:
                print(f"   ✅ 正确显示停车场名称")
        
        # 4. 生成修复总结
        print(f"\n💡 修复效果总结")
        print("-" * 60)
        
        all_records_have_name = True
        for record in user_records:
            details = record.get_details(include_relations=True)
            if 'parking_lot_name' not in details:
                all_records_have_name = False
                break
        
        if all_records_have_name:
            print("✅ 所有停车记录都包含 parking_lot_name 字段")
            print("✅ 前端Dashboard将正确显示停车场名称")
            print("✅ 不再显示'未知停车场'")
        else:
            print("❌ 部分记录缺少 parking_lot_name 字段")
            print("❌ 前端可能仍显示'未知停车场'")
        
        # 5. 数据结构对比
        print(f"\n📊 修复前后数据结构对比")
        print("-" * 60)
        
        if user_records:
            record = user_records[0]
            details = record.get_details(include_relations=True)
            
            print("修复前的数据结构:")
            print("- parking_lot: { id, name, address }")
            print("- 前端访问: record.parking_lot_name ❌ undefined")
            print("- 显示结果: '未知停车场'")
            
            print("\n修复后的数据结构:")
            print("- parking_lot: { id, name, address }")
            print("- parking_lot_name: 'T1' ✅")
            print("- 前端访问: record.parking_lot_name ✅ 'T1'")
            print("- 显示结果: 'T1'")
            
            print(f"\n实际数据示例:")
            print(f"parking_lot_name: '{details.get('parking_lot_name', '不存在')}'")
            if 'parking_lot' in details and details['parking_lot']:
                print(f"parking_lot.name: '{details['parking_lot']['name']}'")

if __name__ == "__main__":
    test_parking_records_fix()
    
    print("\n🎉 停车记录API修复测试完成!")
    print("前端Dashboard现在应该能正确显示停车场名称了。")
